<!-- Generated by SpecStory -->

# Klx P800单机推理benchmark脚本 (2025-08-15 11:12Z)

_**User**_

klx P800单机推理benchmark脚本

---

_**Assistant**_

根据您的工作累积文档，我找到了KLX P800单机推理的benchmark脚本。以下是完整的benchmark命令：

## KLX P800单机推理Benchmark脚本

### 基础版本（100个请求）：
```bash
python3 benchmark_serving.py \
  --host 0.0.0.0 \
  --port 8001 \
  --backend vllm \
  --model /workspace/Qwen2.5-32B-engines/tokenizer \
  --dataset-name random \
  --num-prompts 100 \
  --random-input-len 256 \
  --random-output-len 256 \
  --ignore-eos
```

### 完整版本（64个并发，保存结果）：
```bash
python3 benchmark_serving.py \
  --host 0.0.0.0 \
  --port 8001 \
  --backend vllm \
  --model /workspace/Qwen2.5-32B-engines/tokenizer \
  --dataset-name random \
  --max-concurrency 64 \
  --num-prompts 64 \
  --random-input-len 256 \
  --random-output-len 256 \
  --ignore-eos \
  --save-result \
  --result-dir ./
```

### 参数说明：
- `--host 0.0.0.0`: 监听所有网络接口
- `--port 8001`: 服务端口
- `--backend vllm`: 使用vllm后端
- `--model`: 模型路径（指向tokenizer目录）
- `--dataset-name random`: 使用随机数据集
- `--max-concurrency 64`: 最大并发数
- `--num-prompts 64`: 测试请求数量
- `--random-input-len 256`: 随机输入长度
- `--random-output-len 256`: 随机输出长度
- `--ignore-eos`: 忽略结束标记
- `--save-result`: 保存测试结果
- `--result-dir ./`: 结果保存目录

这个脚本用于测试KLX P800单机推理性能，可以评估模型的吞吐量和延迟等关键指标。

---

