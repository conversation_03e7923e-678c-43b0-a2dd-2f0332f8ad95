离线安装docker

1、安装依赖
dpkg -i containerd.io_1.2.6-3_amd64.deb
2、安装客户端
dpkg -i docker-ce_19.03.3~3-0~ubuntu-xenial_amd64.deb
3、安装服务端
dpkg -i docker-ce-cli_19.03.3~3-0~ubuntu-xenial_amd64.deb
添加docker 网桥(如何安装后服务起不来)
ip link add dev docker0 type bridge
ip addr add dev docker0 ***********/16 brd **************

ip link add dev docker0 type bridge
ip addr add dev docker0 ***********/16 brd **************

ip addr flush dev docker0

docker配置文件
/etc/default/docker

安装docker-compose
mv docker-compose-Linux-x86_64 /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

 systemctl restart docker
systemctl start docker

dpkg --purge containerd.io
dpkg --purge docker-ce
dpkg --purge docker-ce-cli

cc -I$CSDK_DIR/include -L$CSDK_DIR/lib -o device-video device-random.c -lcsdk -lZIMESenderSDK -lZIMEClientSDK

v4l2-ctl --device=/dev/video0 --all

video4linux

devicevideo
|___build
|___includ
       |___edgex、中研头文件
|___lib
     |___edgex、中研so文件
|___src
     |___include
     |___c
|___makefile

git add .
git commit -m "EC-WX:614007647155 video设备服务代码更新"
git commit -m "EC-WX:614010228196 设备服务中研CPU性能优化接口变更修改"
git commit -m "RDC:TCF-4260585 【拨云见雾】服务支持私有技术服务实体/实例创建、资源和环境变量配置等功能-安装"
RDC:TCF-4260555【拨云见雾】组件支持通过enable_ssl环境变量控制开启内部https

git commit -m "RDC:TCF-4260555 【拨云见雾】组件支持通过enable_ssl环境变量控制开启内部https"
git commit -m "RDC:TCF-4440080  cwsm支持多节点P2P压测"
git commit -m "RDC:TCF-4425843  智算套件AICS支持ZF2兼容（虚机部署， 物理机部署 ,支持RISC-V指令集） 组件适配"
git commit -m "RDC:TCF-4697339  openpalette cim节点运行在risc-v架构上(组件输出可用版本）- volcano"
git commit -m "RDC:TCF-4697333  openpalette cim节点运行在risc-v架构上(组件输出可用版本）-wsm"
git commit -m "RDC:TCF-4657737 故障定界故障码描述修改"
git commit -m "RDC:TCF-4697341 openpalette cim节点运行在risc-v架构上(组件输出可用版本）-aiportal"
git commit -m "RDC:TCF-4877708 【自提】cwsm单节点部署cpu request调整为0"
git commit -m "RDC:TCF-4936638 volcano组件arm64版本交叉编译和打包，推送流水线"
git commit -m "RDC:TCF-4937613 apts的arm64流水线上线"
git commit -m "RDC:TCF-4977883 【cwsm】组件适配支持ZF2兼容（虚机部署， 物理机部署 ,支持RISC-V指令集 ）上电联调验证"
git commit -m "RDC:TCF-5071850 【自提】wsm合并riscv分支流水线编译推送脚本"
git commit -m "RDC:TCF-5071851 【自提】volcano合并riscv分支流水线编译推送脚本"
TCF-5071850

产品需求 TCF-4977883: 【cwsm】组件适配支持ZF2兼容（虚机部署， 物理机部署 ,支持RISC-V指令集 ）上电联调验证

git commit -m "RDC:TCF-4724603 【自提】【Director】【cwsm】AIBM融合版部署，helm部署后检查，检查cwsm组件failed"

产品需求 TCF-4425843: 智算套件AICS支持ZF2兼容（虚机部署， 物理机部署 ,支持RISC-V指令集） 组件适配

产品需求 TCF-4260555: 【拨云见雾】组件支持通过enable_ssl环境变量控制开启内部https

git push origin HEAD:refs/for/master
git reset HEAD^
git reset HEAD^
git commit -m "EC-WX:614007647819 设备服务makefile和res配置文件提交"
git diff
git commit -m "EC-WX:614008114024 代码检查工具问题清除"

-m "EC-WX:614010695007 IPC抗干扰参数配置"

不通过敲命令，修改文件
git add .
git commit -a --amend --no-edit
然后
git push origin HEAD:refs/for/master

devicevideo/debug/makefile
devicevideo/debug/objects.mk
devicevideo/debug/sources.mk
devicevideo/debug/subdir.mk
devicevideo/src/res/VideoProfile.yaml
devicevideo/src/res/configuration.toml

/usr/lib/x86_64-redhat-linux6E/lib64/

编译环境：
/usr/local/lib
运行环境
拷贝
/usr/lib/x86_64-linux-gnu/libmicrohttpd.so.10
/usr/local/lib/libcbor.so.0
创软链接
 ln -s libcbor.so.0.0.0 libcbor.so.0

/usr/lib/x86_64-linux-gnu/libmfx.so.1.28
ln -s libmfx.so.1.28 libmfx.so.1

export LIBVA_DRIVERS_PATH=/opt/intelcgsl/mediasdk/lib64
export MFX_HOME=/opt/intelcgsl/mediasdksdk/

Ubuntu编译
EdgexGateway/devicevideo/3rdparty/lib/
拷贝libmicrohttpd.so.10文件到/usr/lib/x86_64-linux-gnu/libmicrohttpd.so.10
拷贝libcbor.so.0.0.0文件到/usr/local/lib/
并创建软链接ln -s libcbor.so.0.0.0 libcbor.so.0

cgsl编译
EdgexGateway/devicevideo/3rdparty/lib/
拷贝libmicrohttpd.so.10文件到/usr/lib/x86_64-linux-gnu/libmicrohttpd.so.10
拷贝libcbor.so.0.0.0文件到/usr/local/lib/
并创建软链接ln -s libcbor.so.0.0.0 libcbor.so.0
拷贝libmfx.so.1.28文件到/usr/lib/x86_64-linux-gnu/
并创建软链接ln -s libmfx.so.1.28 libmfx.so.1

https://github.com/edgexfoundry/device-sdk-c.git

locate前刷新索引
updatedb

cgsl更新源
yum clean all
yum makecache

yum -y update

/etc/yum.repos.d/CGSL-Media.repo文件存在如下配置 。以通用服务器版本为列

name=CGSL-$releasever - Media

baseurl=http://10.75.10.3/os/v5/5.04/V5.04.F11/

enabled = 1

gpgcheck = 0

tar -xvf gcc-5.4.0.tar.bz2
cd gcc-5.4.0
./contrib/download_prerequisites
mkdir build
cd build
../configure --enable-checking=release --enable-languages=c,c++ --disable-multilib
make（建议不要使用make -j来编译，虽然可以缩短编译时间，但极大可能会编译失败）
make install

cd /usr/bin/
mv gcc gcc_back
mv g++ g++_back

ln -s /usr/local/bin/gcc gcc
ln -s /usr/local/bin/g++ g++

tar -czxf cmake-3.15.3.tar.gz
cd cmake-3.15.3
./bootstrap
make
make install

/usr/lib/libstdc++.so.6
/usr/lib64/libstdc++.so.6
/usr/local/lib64/libstdc++.so.6
[root@localhost cmake-3.15.3]# ll /usr/lib64/libstdc++.so.6
lrwxrwxrwx. 1 root root 19 Mar  2  2015 /usr/lib64/libstdc++.so.6 -> libstdc++.so.6.0.19
[root@localhost cmake-3.15.3]# ll /usr/local/lib64/libstdc++.so.6
lrwxrwxrwx. 1 root root 19 Feb 27 07:52 /usr/local/lib64/libstdc++.so.6 -> libstdc++.so.6.0.21
[root@localhost cmake-3.15.3]# ll /usr/lib/libstdc++.so.6
lrwxrwxrwx. 1 root root 19 Mar  2  2015 /usr/lib/libstdc++.so.6 -> libstdc++.so.6.0.19

[root@localhost bin]# cd /usr/lib64/
[root@localhost lib64]# ln -s /usr/local/lib64/libstdc++.so.6.0.21 libstdc++.so.6

 rpm -qa | grep XXXX

rpm -e --nodeps libuuid-d

网关上安装CGSL系统、以及SDI卡驱动、intel的mediasdk
安装docker和docker-compose
加载所有edgex框架相关的微服务镜像，并跑通edgex框架

运行设备服务失败，一系列相关依赖库找不到（设备服务在Ubuntu上编译的）
建立设备服务CGSL的编译环境：（全部需要源码安装或升级）
version of GCC supporting C11.
CMake version 3 or greater and make.
Development libraries and headers for:
curl (version 7.32 or later)
microhttpd (version 0.9)
libyaml (version 0.1.6 or later)
libcbor (version 0.5)
libuuid (from util-linux v2.x)         <<<<<<<<<<-------当前进展在这里

编译device-sdk-c
编译摄像设备服务
联调
容器化     ----容器如何使用SDI和intel的GPU库未知，有风险

公司内源uuid microhttpd
yum install libuuid-devel

yum install pciutils

export LD_LIBRARY_PATH=/usr/local/lib

docker run -it --privileged --net=host

curl -X GET "http://localhost:48080/api/v1/ping"

docker run -t -i --privileged --net=host ubuntu1604:v2 /bin/bash
docker run -t -i --privileged -v /home/<USER>/Downloads/edgex:/hostedgex ubuntu1604:v1
/media/sd-mmcblk0p3/edgex/imagesmake_devicevideo/sdklibs/

docker run -it --privileged -v host目录:/tmp rootfs_full:v1 /bin/sh

/home/<USER>/Downloads/edgex/MediaStack

 apt-get install lsb_release
apt-get install libvlc-dev

/home/<USER>/ZIMEClientSDK_LinuxMEC~/ZIMEClientSDK/bin/Linux/MECSenderDemo

export LD_LIBRARY_PATH=/usr/local/lib
export LIBVA_DRIVER_NAME=iHD
export LIBVA_DRIVERS_PATH=/opt/intel/mediasdk/lib64

dpkg-query --showformat='${Package}: ${Version}\n' --show | grep libva

apt-get install libssl-dev

tar -zxvf zlib-1.2.11.tar.gz
./configure && make && make install
备份启动脚本以及配置文件
cp /etc/init.d/ssh /etc/init.d/ssh.old && cp -r /etc/ssh /etc/ssh.old

./configure --prefix=/usr --sysconfdir=/etc/ssh --with-md5-passwords --with-pam --with-zlib --with-privsep-path=/var/lib/sshd
make && make install

安装redis客户端
 apt-get install redis-tools

cat redis-3.0.7.tar.gz | docker import - redis-3.0.7

docker run -itd  -v /home/<USER>/hostedgex -v /usr/bin:/usr/bin  -v /usr/lib:/usr/lib -v /usr/lib64:/usr/lib64 -v /opt/intel:/opt/intel --net=host --privileged=true

docker run -it  -v /home/<USER>/hostedgex -v /usr/bin:/usr/bin  -v /usr/lib:/usr/lib -v /usr/lib64:/usr/lib64 -v /opt/intel:/opt/intel --net=host --privileged=true

docker run -it  -v /home:/hosthome --net=host --privileged=true

[Registry]
  Host = "localhost"
  Port = 8500
  Type = "consul"

# CheckInterval = "10s"

# FailLimit = 3

# FailWaitTime = 10

127.0.0.1:

docker volume ls
docker volume inspect XXXX

docker inspect

ln -s libmicrohttpd.so.10.22.0 libmicrohttpd.so
ln -s libmicrohttpd.so.10.22.0 libmicrohttpd.so.10
ln -s libyaml-0.so.2.0.6 libyaml-0.so.2
ln -s libyaml-0.so.2.0.6 libyaml.so
ln -s libcbor.so.0.0.0  libcbor.so.0
ln -s libcbor.so.0  libcbor.so
ln -s libcur.so.4.3.0 libcurl.so.4

dmidecode |grep -A 8 "System Information"

yum install libmicrohttpd-devel.x86_64
yum install libuuid-devel.x86_64

yum install libyaml-devel.x86_64 (0.1.4)  编译安装

lsblk

edgex_metadata_client_get_deviceservice
edgex_metadata_client_get_devices

pstree -p PID

http://************:48061/api/v1/logs/originServices/device-virtual/1585541602993/1585708032174/100
时间->时间戳： date +%s

/etc/rsyslog.conf
*.info;mail.none;authpriv.none;cron.none /var/log/messages
修改为
*.none /var/log/messages
重启系统日志
systemctl restart rsyslog

vmware6.7 许可key
5J485-6U250-W8588-0210P-9TZJQ

docker cp /home/<USER>/Downloads/ulls-edgex-gateway/seed/res/properties 5a15adafb0e0:/edgex/cmd/config-seed/res/properties
docker commit 5a15adafb0e0 edgexfoundry/docker-core-config-seed-go:v1

docker cp /home/<USER>/Downloads/ulls-edgex-gateway/seed/res/properties ad1161e78fe4:/edgex/cmd/config-seed/res/properties
docker commit ad1161e78fe4 edgexfoundry/docker-core-config-seed-go:v2

docker-compose 启动问题： mount /tmp -o remount,exec

edgex-device-video                    /bin/sh -c /bin/bash start.sh    Up       0.0.0.0:49990->49990/tcp

公司ntp服务器
外部时间源为***********

ubuntu安装远程vnc
安装dconf-editor
dconf-editor配置
dconf write /org/gnome/desktop/remote-access/require-encryption false
设置桌面共享
settings-> Sharing->Screen Sharing

apt install ibus-libpinyin
apt install ibus-clutter

brctl show

ip netns

mount -o loop CGS-Linux-MAIN.V5.05.F5-x86_64.dvd.iso /root/Downloads/cgsliso
mount -o loop CentOS-7-x86_64-DVD-1908.iso /root/Downloads/centosiso
 rpm -ivh fxload-2002_04_11-16.el7.x86_64.rpm
rpm -ivh alsa-lib-1.1.6-2.el7.i686.rpm

ovftool vi://root:@************/CGSL d:

ovftool --X:logFile=d:\vm\upload1.log --X:logLevel=verbose --noSSLVerify vi://root:@************/Taoyongguang d:\vm  Byjw123!
ovftool --X:logFile=d:\vm\upload1.log --X:logLevel=verbose D:\vm\ZX_DBE_Taoyongguang\ZX_DBE_Taoyongguang.vmx  D:\vm\153\153vm.vof

升级ssh server
安装gcc
apt install gcc
安装依赖库
apt-get install zlib1g.dev
apt-get install libssl-dev

编译openssh版本

yum-config-manager --disable Artifactory

docker info |grep 'Logging Driver'
/etc/systemd/journald.conf
journalctl CONTAINER_NAME=edgex-device-video  --since "2020-06-10 10:00" --until "2020-06-10 11:00"

export DISPLAY=:0
清除xrandr 报 can not open display

xrandr -s  XXXX 设置分辨率

./configure --prefix=/home/<USER>/caninstall/libsocketcan/out --host=arm-xilinx-linux-gnueadi
./configure --host=arm-none-linux-gnueabi 
--prefix=/home/<USER>/caninstall/canutils/out/ 
libsocketcan_LIBS=-lsocketcan 
LDFLAGS="-L/home/<USER>/caninstall/libsocketcan/out/lib" 
libsocketcan_CFLAGS="-I/home/<USER>/caninstall/libsocketcan/out/include"

./configure --host=arm-none-linux-gnueabi --prefix=/home/<USER>/caninstall/libsocketcan-0.0.9/out/ 
libsocketcan_LIBS=-lsocketcan 
LDFLAGS=-L/home/<USER>/caninstall/libsocketcan-0.0.9/out/lib 
CPPFLAGS=-I/home/<USER>/caninstall/libsocketcan-0.0.9/out/include

./configure --host=arm-none-linux-gnueabi 
--prefix=/home/<USER>/caninstall/canutils/out/ 
libsocketcan_LIBS=-lsocketcan 
LDFLAGS="-L/home/<USER>/caninstall/libsocketcan/out/lib" 
libsocketcan_CFLAGS="-I/home/<USER>/caninstall/libsocketcan/out/include"

ip -details link show can0

export DOCKER_RAMDISK=true
dockerd --data-root /run/media/mmcblk0p3/dockerenv
export DOCKER_RAMDISK=true
dockerd --data-root /media/sd-mmcblk0p3/dockerenv &

export DOCKER_RAMDISK=true
dockerd --data-root /paasdata/dockerenv &

export DOCKER_RAMDISK=true
dockerd --data-root /home/<USER>/dockerenv &

export http_proxy=http://proxyhk.zte.com.cn:80/
export all_proxy=socks://proxyhk.zte.com.cn:80/
export ftp_proxy=http://proxyhk.zte.com.cn:80/
export https_proxy=http://proxyhk.zte.com.cn:80/
declare -x no_proxy="10.0.0.0/8,*********/8,.zte.com.cn,localhost,*********/8,::1"

curl -i -v --noproxy *********** -H Content-Type:'application/x-www-form-urlencoded; charset=UTF-8' -X POST http://***********/ac_portal/login.php -d 'opr=pwdLogin&userName=10029157&pwd=Zhuyiwen!358&rememberPwd=0&lang=chs'

curl -i -v --noproxy *********** -H Content-Type:'application/x-www-form-urlencoded; charset=UTF-8' -X POST http://***********/ac_portal/login.php -d 'opr=pwdLogin&userName=*****&pwd=*******&rememberPwd=0&lang=chs'

/usr/local/arm/aarch64_eabi_gcc6.2.0_glibc2.24.0_fp/bin/
aarch64-unknown-linux-gnueabi-gcc

 ./configure --host=aarch64-linux  CC=/aarch64_eabi_gcc6.2.0_glibc2.24.0_fp/bin/aarch64-unknown-linux-gnueabi-gcc   LD=/aarch64_eabi_gcc6.2.0_glibc2.24.0_fp/bin/aarch64-unknown-linux-gnueabi-ld
 ./configure --host=aarch64-linux  CC=aarch64-unknown-linux-gnueabi-gcc   LD=aarch64-unknown-linux-gnueabi-ld

 ./configure --host=aarch64-linux 
 --prefix=/home/<USER>/software/curl 
 CC=/usr/local/arm/aarch64_eabi_gcc6.2.0_glibc2.24.0_fp/bin/aarch64-unknown-linux-gnueabi-gcc 
 LD=/usr/local/arm/aarch64_eabi_gcc6.2.0_glibc2.24.0_fp/bin/aarch64-unknown-linux-gnueabi-ld

 编译libpcap
 ./configure --host=aarch64-linux 
 --prefix=/home/<USER>/projects/tcpdump/tools/ 
 LDFLAGS="-L/home/<USER>/projects/tcpdump/tools/lib/" 
 CPPFLAGS="-I/home/<USER>/projects/tcpdump/tools/include/"

 编译tcpdump
 ./configure --host=aarch64-linux  
 --prefix=/home/<USER>/projects/tcpdump/tools/ 
 LDFLAGS="-L/home/<USER>/projects/tcpdump/tools/lib/" 
 CPPFLAGS="-I/home/<USER>/projects/tcpdump/tools/include/"

 --with-pcap=linux
 ac_cv_linux_vers=2
 --with-pcap=linux

 获取源码：https://curl.haxx.se/download/
 编译curl：
 ./configure --host=aarch64-linux 
 --prefix=/home/<USER>/software/curl 
 CC=aarch64-unknown-linux-gnueabi-gcc 
 LD=aarch64-unknown-linux-gnueabi-ld

 https://ftp.gnu.org/gnu/libmicrohttpd/
 libmicrohttpd-0.9.71.tar.gz
 ./configure --host=aarch64-linux 
 --prefix=/home/<USER>/software/microhttpd 
 CC=aarch64-unknown-linux-gnueabi-gcc 
 LD=aarch64-unknown-linux-gnueabi-ld

 http://sourceforge.mirrorservice.org/l/li/libuuid/
 ./configure --host=aarch64-linux 
 --prefix=/home/<USER>/software/uuid 
 CC=aarch64-unknown-linux-gnueabi-gcc 
 LD=aarch64-unknown-linux-gnueabi-ld

 https://github.com/yaml/libyaml/tags
 ./configure --host=aarch64-linux 
 --prefix=/home/<USER>/software/yaml 
 CC=aarch64-unknown-linux-gnueabi-gcc 
 LD=aarch64-unknown-linux-gnueabi-ld

  ./configure --host=aarch64-linux 
 --prefix=/home/<USER>/software/iperf
 enable-debug

   ./configure --prefix=/home/<USER>/zzn/software/iperf

https://github.com/PJK/libcbor/tags
export CC=aarch64-unknown-linux-gnueabi-gcc
export LD=aarch64-unknown-linux-gnueabi-ld
export CXX=aarch64-unknown-linux-gnueabi-g++

cmake -DCMAKE_BUILD_TYPE=Release
make cbor cbor_shared
make DESTDIR=/home/<USER>/software/cbor install

************编译device-sdk-c**********************************
export CMAKE_LIBRARY_PATH=/home/<USER>/software/microhttpd/lib:/home/<USER>/software/curl/lib:/home/<USER>/software/yaml/lib:/home/<USER>/software/uuid/lib:/home/<USER>/software/cbor/usr/local/lib
export CMAKE_INCLUDE_PATH=/home/<USER>/software/microhttpd/include:/home/<USER>/software/curl/include:/home/<USER>/software/yaml/include:/home/<USER>/software/uuid/include:/home/<USER>/software/cbor/usr/local/include

set (OS_ARCH aarch64)
message (STATUS "SYS env is :${OS_ARCH}")

device-sdk-c/src/
target_include_directories (csdk PRIVATE ../../include ../../../../software/microhttpd/include ../../../../software/uuid/include
  ../../../../software/curl/include ../../../../software/yaml/include ../../../../software/cbor/usr/local/include)

************编译device-sdk-c end**********************************

go tool dist list

CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -o gatewayclient

libcurl.so
libcurl.so.4
ln -s libcurl.so.4.6.0 libcurl.so.4
ln -s libcurl.so.4.6.0 libcurl.so

libmicrohttpd.so
libmicrohttpd.so.12
ln -s libmicrohttpd.so.12.56.0 libmicrohttpd.so.12
ln -s libmicrohttpd.so.12.56.0 libmicrohttpd.so

libcbor.so
libcbor.so.0
ln -s libcbor.so.0.0.0 libcbor.so.0
ln -s libcbor.so.0.0.0 libcbor.so

libuuid.so
libuuid.so.1
ln -s libuuid.so.1.0.0 libuuid.so.1
ln -s libuuid.so.1.0.0 libuuid.so

libyaml.so
libyaml-0.so.2
ln -s libyaml-0.so.2.0.8 libyaml-0.so.2
ln -s libyaml-0.so.2.0.8 libyaml.so

cp /tmp/libmicrohttpd.* ./
cp /tmp/libcbor.* ./
cp /tmp/libyaml-0.* ./
cp /tmp/libuuid.* ./
ln -s libmicrohttpd.so.12.56.0 libmicrohttpd.so.12
ln -s libmicrohttpd.so.12.56.0 libmicrohttpd.so
ln -s libcbor.so.0.0.0 libcbor.so.0
ln -s libcbor.so.0 libcbor.so
ln -s libyaml-0.so.2.0.8 libyaml-0.so.2
ln -s libyaml-0.so.2.0.8 libyaml.so
ln -s libuuid.so.1.0.0 libuuid.so.1
ln -s libuuid.so.1.0.0 libuuid.so

ln -s libcurl.so.4.6.0 libcurl.so.4
ln -s libcurl.so.4.6.0 libcurl.so

./configure --prefix=/home/<USER>/caninstall/libsocketcan/out --host=arm-xilinx-linux-gnueadi
./configure --host=arm-xilinx-linux-gnueadi  
--prefix=/home/<USER>/software/canarm/libsocketcan/out/ 
libsocketcan_LIBS=-lsocketcan 
LDFLAGS="-L/home/<USER>/software/canarm/libsocketcan/out/lib" 
libsocketcan_CFLAGS="-I/home/<USER>/software/canarm/libsocketcan/out/include"

./configure --host=arm-xilinx-linux-gnueadi 
--prefix=/home/<USER>/software/canarm/canutils/out/ 
libsocketcan_LIBS=-lsocketcan 
LDFLAGS="-L/home/<USER>/software/canarm/libsocketcan/out/lib" 
libsocketcan_CFLAGS="-I/home/<USER>/software/canarm/libsocketcan/out/include"

aarch64-unknown-linux-gnueabi-gcc-ar rcs libsf.a sf.o
aarch64-unknown-linux-gnueabi-g++ -c -D_GLIBCXX_USE_CXX11_ABI=0 --std=c++11 -rdynamic MecmqttHash.cpp

docker pull emqx/kuiper@sha256:8ad22048d4e249d97dd94ad3d6877dd4e4fd170492b22dad94abd5b1c6f5916a

telnet配置文件
/etc/pam.d/login
ssh配置文件
/etc/init.d/dropbear
/usr/sbin/dropbear -FE -p 2222
/usr/sbin/dropbear -r /etc/dropbear/dropbear_rsa_host_key -p 22 -w
图形界面启动：startx

设置docker开机启动
systemctl enable docker
usermod -aG docker root（your_username）
reboot

gcc  -g -DBEBUG -std=c11 -I../../include -I../../3rdparty/include -L../../3rdparty/lib/cgsl  -o "device-video" $(OBJS) $(USER_OBJS) $(LIBS)

docker build . 
--build-arg http_proxy=http://proxyhk.zte.com.cn:80/ 
--build-arg https_proxy=http://proxyhk.zte.com.cn:80/ 
--build-arg no_proxy=10.0.0.0/8,*********/8,.zte.com.cn,localhost,*********/8,::1 
-t device-camera-go

交叉编译查询依赖
aarch64-unknown-linux-gnueabi-readelf -a device-video  |grep "Shared"
aarch64-unknown-linux-gnueabi-readelf -a ./libcsdk.so| grep "Shared"

libgstbase-1.0.so.0
libgobject-2.0.so.0
libglib-2.0.so.0
libgmodule-2.0.so.0

media-ctl -p -d /dev/media0

media-ctl -d /dev/media0 -V "\"a00c0000.v_proc_ss\":0  [fmt:UYVY10_1X20/1920x1080 field:none]"
media-ctl -d /dev/media0 -V "\"a00c0000.v_proc_ss\":1  [fmt:VYYUYY10_4X20/1920x1080 field:none]"
media-ctl -d /dev/media1 -V "\"a00d0000.v_proc_ss\":0  [fmt:UYVY10_1X20/1920x1080 field:none]"
media-ctl -d /dev/media1 -V "\"a00d0000.v_proc_ss\":1  [fmt:VYYUYY10_4X20/1920x1080 field:none]"
media-ctl -d /dev/media2 -V "\"a00e0000.v_proc_ss\":0  [fmt:UYVY10_1X20/1920x1080 field:none]"
media-ctl -d /dev/media2 -V "\"a00e0000.v_proc_ss\":1  [fmt:VYYUYY10_4X20/1920x1080 field:none]"
media-ctl -d /dev/media3 -V "\"a00f0000.v_proc_ss\":0  [fmt:UYVY10_1X20/1920x1080 field:none]"
media-ctl -d /dev/media3 -V "\"a00f0000.v_proc_ss\":1  [fmt:VYYUYY10_4X20/1920x1080 field:none]"

断电重启 /media/card 执行脚本
./PLL33M_short

edgexfoundry.org hub.docker.com/r/emqx/kuiper gstreamer.freedesktop.org/ 等等。。。

安装golang1.15
mkdir /usr/local/go1154
tar -C /usr/local/go1154 -xzf ****
/etc/profile
export no_proxy=10.0.0.0/8,*********/8,.zte.com.cn,localhost,*********/8,::1
export GOROOT=/usr/local/go1154/go

下载
go get github.com/rjeczalik/pkgconfig/cmd/pkg-config

安装libzmq
echo "deb http://download.opensuse.org/repositories/network:/messaging:/zeromq:/release-stable/Debian_9.0/ ./" >> /etc/apt/sources.list
wget https://download.opensuse.org/repositories/network:/messaging:/zeromq:/release-stable/Debian_9.0/Release.key -O- | sudo apt-key add
apt-get install libzmq3-dev
export PKG_CONFIG_PATH=/home/<USER>/software/zmq/lib/pkgconfig/
编译zmq
 ./configure --host=aarch64-linux 
 --prefix=/home/<USER>/software/zmq 
 CC=aarch64-unknown-linux-gnueabi-gcc 
 LD=aarch64-unknown-linux-gnueabi-ld 
 CXX=aarch64-unknown-linux-gnueabi-g++

git clone -b geneva https://github.com/edgexfoundry/edgex-go.git

编译redis
make MALLOC=libc

make make PREFIX=/some/other/directory install

netstat -tunlp

    switch(gDevType){
      case enumDevType_Video:
      case enumDevType_Audio:
      case enumDevType_IpVideo:
      case default:
        return false;
    }

wget --http-user=10029157 --http-password='Zhuyiwen!2020' -r -l1 -np -nd https://artxa.zte.com.cn/artifactory/zxsvp-release-generic/CGSL-V5.04.F29B5_20201226/versions/CGSL_Packages/CGS-Linux-MAIN.V5.04.F29B5-x86_64/rpms/
wget --http-user=10029157 --http-password='Zhuyiwen!2020' -r -l1 -np -nd https://artxa.zte.com.cn/artifactory/zxsvp-release-generic/CGSL-V5.04.F29B5_20201226/versions/CGSL_CORE_Packages/CGS-Linux-CORE.V5.04.F29B5-x86_64/rpms

declare -x no_proxy="10.0.0.0/8,*********/8,.zte.com.cn,localhost,*********/8,::1"

Get-VM -Name mimosa7.4 | Get-VMNetworkAdapter | Where-Object { $_.MacAddress -eq "00155D2C2021" } | Set-VMNetworkAdapter -MacAddressSpoofing On
Get-VM -Name mimosa7.4 | Get-VMNetworkAdapter | Where-Object { $_.MacAddress -eq "00155D2C2021" } | Get-VMNetworkAdapter -MacAddressSpoofing On

Byjw13%&
*************vmware*************
Byjw@mec13%(登入web)
ubuntun/Mec@13%&
************* zte/Byjw147258369!@#

zxmec-read/123zxmec-read

*************，tecs/TCms@Zte3
root/RCms@Zte3

my.vmware.com/<EMAIL>/Zhuyiwen@123

service --status-all

x86上设置模拟arm64指令，可以x86可以docker打包arm64
docker run --rm --privileged multiarch/qemu-user-static --reset -p yes
docker run --rm --privileged multiarch/qemu-user-static --reset -p no

查看容器中系统版本
cat /etc/issue

faststone capture
bluman
VPISCJULXUFGDDXYAUYF

机器学习和人工智能软件 FogHorn

sudo ufw allow smtp　允许所有的外部IP访问本机的25/tcp (smtp)端口
sudo ufw allow 22/tcp 允许所有的外部IP访问本机的22/tcp (ssh)端口
sudo ufw allow 53 允许外部访问53端口(tcp/udp)
sudo ufw allow from ************* 允许此IP访问所有的本机端口
sudo ufw allow proto tcp from *********** to *********** port 22允许***********访问***********的22/tcp（ssh）端口
sudo ufw deny smtp 禁止外部访问smtp服务
sudo ufw delete allow smtp 删除上面建立的某条规则

00155D2C201f
00155D2C2020
00155D2C2021

consul://edgex-core-consul:8500

curl -X GET http://edgex-core-consul:8500/v1/status/leader
curl -X GET http://edgex-core-command:48082/api/v1/ping
curl http://edgex-core-consul:8500/v1/status/leader

curl -i -X PATCH -H "Content-Type:application/json" -d '{"application":"inetmanager","operation":{"treeOperKey":"edit-config","treeOperValue":"create"},"confs":[{"items":{"net_ex_cidr_v4":"***********/24","net_ex_gw_v4":"*************","net_ex_vip_v4": "*************"}}]}' http://************:1180/pconf/v1/tenants/admin/treeconfs

************/221
ubuntu/Mec12@34
tecs/TCms@Zte3
root/RCms@Zte3
mep/Srv2Tcf#MecMini

ace/Srv2MecPlugin#MecMini

kubectl get network --all-namespaces
kubectl get pod --all-namespaces

v4l2-ctl --list-devices
v4l2-ctl -d /dev/video0 --all

250服务器开设了新账号做xilinx的编译环境：
系统：ubuntu18.04
IP：**************
用户名：xilinx
密码：awdr!QSEF1

zte@zte-Skylake:~/test$ curl -X POST 'http://**********:8888/gwmgt/secure/enable_uart'
{"result":true}
zte@zte-Skylake:~/test$ curl -X POST 'http://**********:8888/gwmgt/secure/enable_all_ports'
{"result":true}

./xyavta -s416x416 -F /dev/video1 -c3 -n10 --skip 7 -f BGR24

curl  http://localhost:8888/gwmgt/datacard/frequency

curl  http://localhost:8888/gwmgt/datacard/simStatus
curl  http://localhost:8888/gwmgt/datacard/slot
curl  http://localhost:8888/gwmgt/product/sn

curl -i -X POST -k -H 'Content-Type:application/json' -i 'http://localhost:8888/gwmgt/datacard/change_sim' --data '{"sim" : "2"}'

************
ubuntu/UbuntU1!2@3#4$
ace/Srv2MecPlugin#MecMini

curl http://edgex-core-data:48080/api/v1/event/count
curl http://edgex-core-data:48080/api/v1/reading/count

*************
ssh:
root/Byjw1Tiger2@3
vnc:
Byjw@Wh2

https://************:30002/api/weblmt/#/login
ace/Srv2MecPlugin#MecMini
*************
root/Byjw147258369!#%
/home/<USER>/0917/device-virtual-arm64-0917-R142.tar

无法监控（28Hub用途交换机）Hub用途的交换机，未配置IP，不做监控上线要求；管理用途的交换机，有配置IP，必须监控上报

219276240544
219276240544
./gateway_run.sh  --cloud=***********:10000  --registry=************:2512  --https-server=***********:10002  --edgecore-root=/paasdata/  --flannel-iface=usb0

https://github.com/Xilinx/Vitis-Tutorials/blob/2021.1/Vitis_Platform_Creation/Introduction/02-Edge-AI-ZCU104/step2.md

https://github.com/Xilinx/Vitis-Tutorials/blob/2021.1/Vitis_Platform_Creation/Introduction/02-Edge-AI-ZCU104/step4.md

docker run --rm --privileged -it -v /usr/lib:/usr/lib
docker run --rm -it -v /home/<USER>/projects/:/home gccarmbuilder:v1 sh
docker run --rm -it -v /home/<USER>/download/:/home gccarmbuilder:v1 sh

./docker_run.sh xilinx/vitis-ai-gpu:latest

1、量化模型
 python yolox_s_quant.py --quant_mode calib --subset_len 200
2、量化模型求值
 python yolox_s_quant.py --quant_mode test
3、生成 xmodel
 python yolox_s_quant.py --quant_mode test --subset_len 1 --batch_size=1 --deploy

 vai_c_xir -x /workspace/projects/yolox_s/quantize_result/YoloBody_int.xmodel  -a /workspace/projects/yolox_s/quantize_result/quant_info.json -o /workspace/projects/yolox_s/compile_result -n yolox_s

 xdputil status

 https://github.com/Xilinx/Vitis-AI-Tutorials/tree/master/Design_Tutorials/09-mnist_pyt

编号 语言 安全编码规则 检查项是否通过
3 C++ CON50-CPP. 不要销毁一个正在被锁定的互斥对象 通过
4 C++ CON51-CPP. 确保异常条件下持有的锁被释放 通过
5 C++ CON52-CPP. 多个线程访问位字段时需要防止数据竞争 通过
6 C++ CON53-CPP. 按预定义的顺序加锁来避免死锁 通过
7 C++ CON56-CPP. 不要刻意地锁一个已经被其它线程持有的非递归锁 通过
8 C++ DCL55-CPP. 避免跨信任边界传递类对象时发生信息泄露 通过
10 C 2.3 PRE32-C不要在类函数宏调用中使用预处理器指令  通过
11 C 14.1 CON30-C 清除线程相关存储  通过
12 C 14.3 CON32-C 多线程访问位域时防止数据冲突  通过
13 C 14.5 CON34-C 为线程共享对象声明恰当的存储生命周期  通过
14 C 14.6 CON35-C 以预定义顺序加锁以避免死锁  通过
15 C 14.13 CON43-C 多线程代码中不允许数据竞争  通过
16 C 新增：密码硬编码 通过
17 C 新增：敏感信息不能明文传输或保存 通过
18 C 新增：不安全的共享内存创建 通过
19 C 新增：不安全的文件操作 通过
20 C++ 多线程使用内存时，需要判断内存是否已经分配。内存未分配时，不能处理业务流程。

docker ps -a|grep "Exited"|awk '{print $1}'|xargs docker rm
docker images|grep none|awk '{print $3}'|xargs docker rmi

To uninstall the CUDA Toolkit, run cuda-uninstaller in /usr/local/cuda-11.2/bin

service vsftpd stop
/sbin/sysctl -e -p /etc/sysctl.conf

docker import xxx.tar imagename:tag

gst-launch-1.0 v4l2src device=/dev/video2 io-mode=4 ! video/x-raw\(memory:XLNXLL\), format=NV12_10LE32, width=1920, height=1080, framerate=60/1 ! omxh265enc num-slices=8 gop-length=120 periodicity-idr=240 cpb-size=6000 control-rate=low-latency prefetch-buffer=true target-bitrate=2000 max-picture-sizes='<250,50,0>' max-qp=43 gop-mode=low-delay-p ! video/x-h265, alignment=nal ! queue max-size-buffers=0 ! rtph265pay ! udpsink buffer-size=60000000 host=************ port=5004  max-lateness=-1 qos-dscp=60 async=false  max-bitrate=120000000

tar --no-same-owner -xvf

root /Zxtj2022~

sysv-rc-conf 设置字服务自启动

天津的bsp版本服务器 ************** zte/ZXMW.ne8250

播放端  *************  test  ZXMW.ne8250   ZXMW.cpe9102
网关所在PC   **************    liuzongliang      ZXMW.ne8250
网关 **************    admin     ZXMW.ne8250
NE     **************  tecs/TCms@Zte3

ssh上去后切到root用户，执行如下操作：
/ushell
zte/Zxtj2013
sh 1
g_IsDockerUpgrade=1
docker run --rm --privileged -it -v /usr/lib:/usr/lib
docker run --rm --privileged -it -v /home/<USER>/projects/:/home gccarmbuilder:v1 sh
 docker run -it --rm --privileged=true --network video_edgex-network -v /paasdata/cameratest/:/paasdata/plc --name dpu 20223bc7d25e sh
 docker run -it --rm --privileged=true -v /paasdata/cameratest/:/paasdata/test 20223bc7d25e sh
docker run -it --rm --privileged=true -v /home/<USER>/Vitis-AI/demo/VART/adas_detection/:/home/<USER>
/home/<USER>/Vitis-AI/demo/VART/adas_detection/

ps aux | sort -k4,4nr | head -n 10

ps -eo pid,ppid,%mem,%cpu,cmd --sort=-%cpu | head

ps axwf -eo pid,stat | grep D
ps -eL -eo pid,stat,pcpu   | grep D

/paasdata/xyavta -s416x416 -c3 -n10 /dev/video3 --enum-format -f RGB24 -F abc

gst-launch-1.0 v4l2src device=/dev/video3 io-mode=4 ! video/x-raw\(XLNX\), width=416, height=416, format=RGB, framerate=60/1 ! queue ! fakesink

Zhuyiwen!358

du -h -d 1
dockerd -D --data-root /paasdata/dockerenv
dockerd --data-root /paasdata/dockerenv &

ZTE_NE_XR
Zte@1358

https://update.code.visualstudio.com/1.60.2/win32-x64/stable
https://update.code.visualstudio.com/1.58.1/win32-x64/stable

linux 下查看 so库 符号表
nm -D 7z.so
objdump -tT 7z.so
nm --demangle --dynamic --defined-only --extern
readelf -Ws

--depth=1

git clone -b v1.1.5 --depth=1 https://github.com/tkestack/gpu-manager.git

lspci -s 86:00.0 -v

kubectl describe node | grep tencent.com/vcuda-core

uname -m && cat /etc/*release
./NVIDIA-Linux-x86-285.05.09.run     --uninstall

./NVIDIA-Linux-x86_64-410.129-diagnostic.run -s --add-this-kernel

./NVIDIA-Linux-x86_64-410*.run -x

kubectl delete pod -n kube-system gpu-admission
kubectl apply -f gpu-admission.yaml

kubectl delete pod -n kube-system gpu-admission
kubectl apply -f gpu-admission.yaml
kubectl get pods --all-namespaces
 kubectl get pods --all-namespaces -o wide --field-selector spec.nodeName=10.0.0.15

kubectl describe node | grep tencent.com/vcuda-core -5

kubectl describe node | grep zte.com.cn/vcore -5

readelf -Ws

MEC  6578：10.230.65.78 ,  ubuntu / UbuntU1!2@3#4$ ,  该环境已下电

超融合 ：  10.230.60.101    ，ssh ： ubuntu / UbuntU1!2@3#4$  , R88 UME 网管：UME环境：https://10.226.208.235:28001/rportal  人事登录 或	admin/Zenap_123!1  ，网元 60101  ，建链地址：10.230.60.90 ， 建链用户名和密码：conap ， CloudOAM_2430!@#， 端口网管默认； cpe 调试机 ：  10.230.65.80  ，远程： rnc /  Aa@888888	; idos 网管：https://10.230.60.101:28201/eportal/#/home     用户名和密码：   admin/Idos_1!2@3#4$

Tcf@169#%*

/sbin/modprobe nvidia-uvm D=`grep nvidia-uvm /proc/devices | awk '{print $1}'`

Driver:   Not Selected
Toolkit:  Installed in /usr/local/cuda-11.6/

Please make sure that

- PATH includes /usr/local/cuda-11.6/bin
- LD_LIBRARY_PATH includes /usr/local/cuda-11.6/lib64, or, add /usr/local/cuda-11.6/lib64 to /etc/ld.so.conf and run ldconfig as root

To uninstall the CUDA Toolkit, run cuda-uninstaller in /usr/local/cuda-11.6/bin
***WARNING: Incomplete installation! This installation did not install the CUDA Driver. A driver of version at least 510.00 is required for CUDA 11.6 functionality to work.
To install the driver using this installer, run the following command, replacing `<CudaInstaller>` with the name of this run file:
    sudo `<CudaInstaller>`.run --silent --driver

apt-get install libxml2

10.230.60.236
ubuntu  UbuntU1!2@3#4$

/home/<USER>/zzn/eFS/eFSC/env/:/home/<USER>

fuser -v /dev/nvidia*
ls -lh libtorch/lib/*.so*

-C
       --demangle[=style]
           Decode (demangle) low-level symbol names into user-level names.  Besides removing any initial underscore prepended by the system, this makes C++
           function names readable. Different compilers have different mangling styles. The optional demangling style argument can be used to choose an
           appropriate demangling style for your compiler.【将低级符号名解码(demangle)成用户级的名字，使得C++函数名具有可读性。为了区分重载函数，c++编译器会将函数返回值/参数等信息附加到函数名称中去组成一个mangle过的符号。这里的-C选项就是做一个逆操作，输出其原始可理解的符号名称】

kubectl delete pod {podname} -n {namespace}

安装软件包：sudo dpkg -i [包名]
查看已安装：dpkg --get-selections
卸载已安装：sudo dpkg -r [包名]

nsenter -t 2905499 -n tcpdump -i any -nn

5300  bmc密码重置
root/superuser   ssh登入
用SSH远程登录BMC的地址,用户名：root，密码：superuser

bmc地址：193.4.17.8
zteroot/superuser

cd到根目录：cd /
进入FLASH0文件夹：cd /FLASH0s
删除USER_DB.db文件：rm USER_DB.db
ls 查看是否还有USER_DB.db文件：ls
重启BMC：reboot

10.230.61.108
zte/OutMan1!2@3#4$

10.230.59.239远程桌面：Tcf@1358
Tcf@169#%*
Tcf@13%*

nsight compute
apt install libxcb-xinerama0

解决xorg占用gpu
/usr/share/X11/xorg.conf.d/nvidia-drm-outputclass.conf删除或改名为nvidia-drm-outputclass.conf.bak

DOCA
nagnum io

docker run -it --gpus all --rm -v /home/<USER>/projects/rcuda/cricket2/:/cricket/ cricket_build:v1
docker run -it  --rm -v /home/<USER>/projects/rcuda/cricket2/:/cricket/ cricket_build:v1
udo -i service portmap stop

sudo -i rpcbind -i -w
sudo -i service portmap start

LD_PRELOAD=/cricket/bin/cricket-server.so:/cricket/bin/libtirpc.so /cricket/tests/bin/bandwidthTest
REMOTE_GPU_ADDRESS=********** LD_PRELOAD=/cricket/bin/cricket-client.so:/cricket/bin/libtirpc.so /cricket/tests/bin/bandwidthTest

    - name: NVIDIA_DRIVER_CAPABILITIES
          value: all
        - name: NVIDIA_VISIBLE_DEVICES

curl http://zcube-schd-extender-svc:3456/zcube-scheduler/filter
curl http://zcube-schd-extender-svc:3456/zcube-scheduler/predicates
curl http://gpu-schd-extender-svc:12345/gpu-scheduler
curl http://*************:2626/metric

runtimeClassName
docker run -e NVIDIA_VISIBLE_DEVICES=all --pid=host --runtime=nvidia -it --rm

kubectl get pods  -n kube-system -o wide
kubectl get svc -n kube-system

spec：
  hostPID： true
  hostIPC： true

第一个K8S集群环境 ：控制节点（原107环境）：************ ， ssh ：pict/PicT1!2@3#4$   用 su - root 的方法切换到root，密码为：RCms@Zte3 ，
				  计算节点（原来108环境）：*********** ， 登录方式：在控制节点上，ssh ubuntu@*********** sudo -i切换root
				 web 登录：web：https://************/   admin/PaaS_1024

第二个K8S集群环境 ：控制节点（原101环境）：************ ，ssh： pict/PicT1!2@3#4$   用 su - root 的方法切换到root，密码为：RCms@Zte3
				 web：https://************/   admin/PaaS_1024 ， 当前只有控制节点

curl --header "Content-Type: application/json-patch+json" 
  --request PATCH 
  --data '[{"op": "remove", "path": "/status/capacity/zte.com.cn~1vcore"}]' 
  http://localhost:8001/api/v1/nodes/***********/status

curl --header "Content-Type: application/json-patch+json" 
  --request PATCH 
  --data '[{"op": "remove", "path": "/status/capacity/zte.com.cn~1vmemory"}]' 
  http://localhost:8001/api/v1/nodes/***********/status

sftp -o "ProxyJump pict@***************" zhuzhengnan@*************

mstsc /w:1920 /h:1080

/root/zartcli/zartcli -o=upload -i=kube-system -m=image -n=zcube-manager -v=V2023.03.27.01 -p=/GFS_DIR/taotao
/root/zartcli/zartcli -o=query -i=kube-system -m=image -n=*zcube-manager
/root/zartcli/zartcli -o=delete -i=kube-system -m=image -n=zcube-manager

/root/zartcli/zartcli -o=upload -i=kube-system -m=image -n=metagpu -v=V2023.05.25.01 -p=/GFS_DIR/zzn/deploy/
/root/zartcli/zartcli -o=query -i=kube-system -m=image -n=*metagpu
/root/zartcli/zartcli -o=upload -i=kube-system -m=image -n=metagpu -v=V2023.06.07.01 -p=/home/<USER>/

安装qemu
apt-get install qemu-kvm
apt-get install qemu
apt-get install virt-manager
apt-get install virt-viewer
apt-get install libvirt-bin
apt-get install bridge-utils
解决lvm2安装问题
sudo systemctl unmask lvm2-lvmpolld.service
sudo systemctl restart lvm2-lvmpolld libvirtd

查询僵尸进程
ps -A -ostat,ppid,pid,cmd |grep -e '^[Zz]'

wks-client --addr /etc/vcuda/vcuda.sock --bus-id  --pod-uid 7a57f145-59d2-4ed5-889a-06100b0706aa --cont-id d4805737bab134166a396f165eeeea2ee161bc11
  snprintf(syscmd, sizeof(syscmd), "/usr/local/nvidia/bin/wks-client --addr %s --bus-id %d --pod-uid %d --cont-id %d",
           RPC_ADDR, bus_id, pod_uid, container);

apt list --installed

docker ps --no-trunc
容器中查询host PID
grep threads /proc/*/sched
docker inspect containerName |grep Pid

stress -c 5 &
stress --vm 2 --vm-bytes 5000M --vm-keep &

如何彻底卸载软件呢？ 可按如下步骤：

sudo apt-get --purge remove `<package>`				# 删除软件及其配置文件
sudo apt-get autoremove `<package>`					# 删除没用的依赖包
sudo dpkg -l |grep ^rc|awk '{print $2}' |sudo xargs dpkg -P		# 清理dpkg的列表中有“rc”状态的软件包

kubectl get ServiceMonitor -n monitoring|grep redis

apt-get install linux-kernel-headers kernel-package

 lspci -nnv | grep "Kernel driver in use" -C 10  查询内核驱动

 cat /lib/modules/$(uname -r)/modules.builtin | grep vfio

/usr/share/X11/xorg.conf.d/nvidia-drm-outputclass.conf

GPU 0: Tesla T4 (UUID: GPU-3708b633-fc9d-41ad-6e1e-2b27470a8014)
GPU 1: Tesla T4 (UUID: GPU-dc034360-be9a-fe2b-a8c2-327fb63c8438)

go test -v ./pkg/predicate/

ln -s libnvidia-ml.so.1 libnvidia-ml.so

export PATH=$PATH:/opt/cov-analysis-linux64-2021.03/bin
cov-configure --gcc  --cuda
cov-configure -co /usr/bin/cc -p gcc

 cov-build --dir cov_result make
 cov-analyze --dir cov_result  --cpp --all -aggressiveness-level high --rule --enable-constraint-fpp --enable-callgraph-metrics
 cov-format-errors --dir cov_result --lang zh-cn --html-output cov_result/html

 libsubunit-dev libsubunit0
 apt install autoconf automake libtool

 /home/<USER>/build/src/libcheck.so.0
/home/<USER>/build/src/libcheck.a
/home/<USER>/build/src/libcheck.so
/home/<USER>/build/src/libcheck.so.0.14.0
/usr/lib/x86_64-linux-gnu/libcheck_pic.a
/usr/lib/x86_64-linux-gnu/libcheck.a

/nc/ncu --export /tmp/var/test2 --force-overwrite --target-processes all --replay-mode kernel --kernel-name-base function --launch-skip-before-match 0 --section-folder /tmp/var/sections --section ComputeWorkloadAnalysis --section InstructionStats --section LaunchStats --section MemoryWorkloadAnalysis --section MemoryWorkloadAnalysis_Chart --section MemoryWorkloadAnalysis_Tables --section Nvlink_Tables --section Nvlink_Topology --section Occupancy --section SchedulerStats --section SourceCounters --section SpeedOfLight --section SpeedOfLight_RooflineChart --section WarpStateStats --sampling-interval auto --sampling-max-passes 5 --sampling-buffer-size 33554432 --profile-from-start 1 --cache-control all --clock-control none --apply-rules yes --import-source no --check-exit-code yes
docker run -it --rm --gpus=1 -v /usr/local/cuda-11.6/:/nc -v /tmp/var/:/tmp/var --cap-add=SYS_ADMIN e90826190365 bash

/tmp/var/target/linux-desktop-glibc_2_11_3-x64/ncu --export /tmp/var/test2 --force-overwrite --target-processes all --replay-mode kernel --kernel-name-base function --launch-skip-before-match 0 --section-folder /tmp/var/sections --section ComputeWorkloadAnalysis --section InstructionStats --section LaunchStats --section MemoryWorkloadAnalysis --section MemoryWorkloadAnalysis_Chart --section MemoryWorkloadAnalysis_Tables --section Nvlink_Tables --section Nvlink_Topology --section Occupancy --section SchedulerStats --section SourceCounters --section SpeedOfLight --section SpeedOfLight_RooflineChart --section WarpStateStats --sampling-interval auto --sampling-max-passes 5 --sampling-buffer-size 33554432 --profile-from-start 1 --cache-control all --clock-control none --apply-rules yes --import-source no --check-exit-code yes

docker run -it -e NVIDIA_DRIVER_CAPABILITIES=all  -e NVIDIA_VISIBLE_DEVICES=all  --runtime=nvidia -v /paasdata/offline/xlab_test_llm:/xlab_test_llm debain-py39-langchain:1.1  bash

docker run -it -e NVIDIA_DRIVER_CAPABILITIES=all  -e NVIDIA_VISIBLE_DEVICES=all  --runtime=nvidia -v /paasdata/offline/tcf-tmp/zzn/nsight_systems/:/ns --privileged=true --pid=host e90826190365 bash

docker run -it -e NVIDIA_DRIVER_CAPABILITIES=all  -e NVIDIA_VISIBLE_DEVICES=all  --runtime=nvidia  --privileged=true --pid=host 71eaf13299f4 bash
docker run -it --rm   -e NVIDIA_VISIBLE_DEVICES=all  --runtime=nvidia  --pid=host 71eaf13299f4 bash

docker run -it -e NVIDIA_DRIVER_CAPABILITIES=all  -e NVIDIA_VISIBLE_DEVICES=0  --runtime=nvidia

docker run -it -e NVIDIA_VISIBLE_DEVICES=0  --runtime=nvidia  镜像名  bash

ctr -n k8s.io run -t --rm --env NVIDIA_VISIBLE_DEVICES=0 --runc-binary=/usr/local/nvidia/toolkit/nvidia-container-runtime swr:2512/admin/dcgm:v1 bx-gpu-test sh
nerdctl -n k8s.io ps

ctr -n k8s.io run -t --rm --env NVIDIA_VISIBLE_DEVICES=0 --runc-binary=/usr/local/nvidia/toolkit/nvidia-container-runtime swr:2512/admin/tf2.4_gpu:0428 bx-gpu-test sh
ctr -n k8s.io run -t --rm --env NVIDIA_VISIBLE_DEVICES=1 --runc-binary=/usr/local/nvidia/toolkit/nvidia-container-runtime swr:2512/admin/tf2.4_gpu:0428 bx-gpu-test2 sh
nerdctl
nsys profile --stats=true --sample=cpu --trace=cuda,cudnn,cublas,nvtx,osrt,oshmem --cudabacktrace=kernel:1000000,sync:1000000,memory:1000000 --sampling-period=1000000 --sampling-trigger=cuda,perf --delay=10 --duration=60 --wait=all

 ./protoc --go_out=. ./*.proto

  nshieldrules off
 nshieldrules on

重启daemonset，不能直接删除pod；没有deployment，直接删除pod，pod不会重启
kubectl rollout restart ds/wks-manager-daemonset -n kube-system
kubectl get ep -n kube-system

1、/etc/docker/daemon.json
{
"insecure-registries":["0.0.0.0/0"]
}

2、/etc/resolv.conf
nameserver *********

3、重启docker  systemctl restart docker

4、docker pull zxnp-pict-release-docker.artsh.zte.com.cn/os/alpine:3.13.8

5、Dockerfile 中添加
  FROM zxnp-pict-release-docker.artsh.zte.com.cn/os/alpine:3.13
  #安装rpm命令
  RUN apk add rpm

  kubectl exec -it test-slice-gpu -n zaip bash

  lspci -nnk -d 10de:15f8

  echo "10de 15f8" > /sys/bus/pci/drivers/pci-stub/new_id
  echo 0000:82:00.0 > /sys/bus/pci/devices/0000:82:00.0/driver/unbind
  echo 0000:82:00.0 > /sys/bus/pci/drivers/pci-stub/bind

  echo "10de 15f8" > /sys/bus/pci/drivers/vfio-pci/new_id
  echo 0000:83:00.0 > /sys/bus/pci/devices/0000:83:00.0/driver/unbind
  echo 0000:83:00.0 > /sys/bus/pci/drivers/vfio-pci/bind

  echo "10de 15f8" > /sys/bus/pci/drivers/vfio-pci/new_id
  echo 0000:82:00.0 > /sys/bus/pci/devices/0000:82:00.0/driver/unbind
  echo 0000:82:00.0 > /sys/bus/pci/drivers/vfio-pci/bind

  agent
  export no_proxy=10.*.*.*,*.zte.com.cn,192.168.*.*,*.zte.intra && curl --noproxy "*" --insecure -sS -g https://************:8088/api/kite/v1/plugin/download?key=9bfc2704-8553-49d0-b52a-a4a546e46a99\&fileName=wxsec-agent-ws-linux-install.sh -o wxsec-agent-ws-linux-install.sh && chmod +x wxsec-agent-ws-linux-install.sh && ./wxsec-agent-ws-linux-install.sh amd64_latest 9bfc2704-8553-49d0-b52a-a4a546e46a99 ************:8088 null false null null
  椒图
  mkdir -p agent_tmp && cd agent_tmp && curl -o wsssr_defence_agent_8.0.5.8612_x64.tar.gz http://10.41.135.244:7901/file/dl/4c82081d8524f4b7ada81c51375661e89e64cdb8e00b706266550f597cbd464a?dir=installPkg && tar -xzvf wsssr_defence_agent_8.0.5.8612_x64.tar.gz && chmod +x ./*_install/install && ./*_install/install -c 10.41.135.244 -U 1d7a998739a84ce5af9b3fe1ececc6ec  -d

  查询容器长ID
  docker inspect -f '{{.ID}}' e99cbb3b10bb

  sk-yRxbyhxwsZwZqWTpAdA0B29eC6Ba40FbAa380b66Fc9b1bB7

kubectl exec -it op-adrm-nvidiagpu-device-plugin-v7.24.10.05.17415611.1-d2d89 sh -n admin

kubectl get pods -n admin -o wide
kubectl get pods -A -o wide |grep wks
kubectl get pods -A  -o wide

nerdctl -n k8s.io ps

环境10.166.194.152   pict   PicT1!2@3#4$
web：https://10.166.194.152 /   admin/debug1@ZTE

训练：python tf_cnn_benchmarks.py --batch_size=32 --model=resnet50 --num_batches=1000
推理：time python tf_cnn_benchmarks.py --batch_size=32 --forward_only --model=resnet50 --num_batches=100000

lspci -vvvt

ethtool -i ens8f1

systemctl restart NetworkManager
nmcli con show
export WKS_VGPU=1
export WKS_RATIO=20
export WKS_GMEM=1024
export SERVER_ADDR=ucx+tcp://192.169.1.33:34095

/home/<USER>/zzn/virtctl console cgsl-zzn -n admin

export LD_LIBRATY_PATH=/usr/lib64/wks-client/:${LD_LIBRATY_PATH}
scp  ubuntu@10.235.189.227:/paasdata/op-data/op-wks-operator/wks-client/libwks-client.so /usr/lib64/wks-client/

kubectl.kubernetes.io/api/v1/namespaces/admin/secrets/toposecret \

找有/paasdata/offline/Deploy_Version_Package/sources/Director目录的节点部署,
如果有/paasdata/offline/Deploy_Version_Package/sources/Director/Director_aif_V1.0.0/business-images路径，
则将cwsm版本拷贝到该路径，没有需要自己创建目录。将aif.desc考到/paasdata/offline/Deploy_Version_Package/sources/Director/Director_aif_V1.0.0/config路径下。
将cwsm的版本放到/paasdata/offline/Deploy_Version_Package/sources/Director/Director_aif_V1.0.0/business-images路径，
进入/paasdata/offline/Deploy_Version_Package/sources/Director/Director_aif_V1.0.0/business-images/cwsm路径删除旧的版本。
到/paasdata/offline/Deploy_Version_Package/install-script路径下运行下面命令
sh deploy.sh upgrade package target=Director_aif_V1.0.0
进行部署cwsm

kubectl get replicationcontroller -A | grep cwsm
cwsm pod 是由replicationcontroller 拉起来的

33AIBM环境信息
   AIBM三节点   ************-72   pict/PicT1!2@3#4$  root/RCms@Zte3
   ngportal登录方式：https://************:443  admin/PaaS1!2@3#4$
   前端登录方式:https://*************/  admin/Zenap_123

   1、okiv5 uninstall --product Director --group pm  --namespace director
2、paas页面删镜像
3、 okiv5 upgrade package --dir /paasdata/offline/Deploy_Version_Package/CMS-PLAT --resource resources/aio.resource --target /paasdata/offline/Deploy_Version_Package/CMS-PLAT/sources/Director/Director_pm_V8.25.10.01.n20241211102505.tar.gz

/home/<USER>/oki/okiv5 upgrade package --dir /home/<USER>/app/AIBM_CN --resource /home/<USER>/app/AIBM_CN/resources/aio.resource --target /home/<USER>/app/AIBM_CN/sources/DirectorExt/DirectorExt_cwsm_V8.24.40.06.n202412251134.tar.gz

https环境
director环境：
uportal：https://10.230.110.55/uportal    admin/Zenap_1235
ngportal：https://10.230.110.55:1043/ngportal    admin/Zenap_1235
https://10.230.110.55:1043/iui/microservices/
后端：10.230.110.56    pict/PicT1!2@3#4$  root/RooT1!2@3#4$
版本包路径：/paasdata/Director-V8.25.10.04-02070045.x64-TCF/

找有/paasdata/offline/Deploy_Version_Package/sources/Director目录的节点部署,
如果有/paasdata/offline/Deploy_Version_Package/sources/Director/Director_aif_V1.0.0/business-images路径，则将cwsm版本拷贝到该路径
没有需要自己创建目录。将aif.desc考到/paasdata/offline/Deploy_Version_Package/sources/Director/Director_aif_V1.0.0/config路径下。
将cwsm的版本放到/paasdata/offline/Deploy_Version_Package/sources/Director/Director_aif_V1.0.0/business-images路径，
进入/paasdata/offline/Deploy_Version_Package/sources/Director/Director_aif_V1.0.0/business-images/cwsm路径删除旧的版本。
到/paasdata/offline/Deploy_Version_Package/install-script路径下运行下面命令
sh deploy.sh upgrade package target=Director_aif_V1.0.0
进行部署cwsm
查找oki日志查看失败详情，oki日志目录：/var/log/oki

curl -s swr:6000/swr/v1/tenants/zenap/images?name=gobase

在aio-lcm容器里
1、okiv5 uninstall --product Director --group pm  --namespace director
2、paas页面删镜像
3、 okiv5 upgrade package --dir /paasdata/offline/Deploy_Version_Package/CMS-PLAT --resource resources/aio.resource --target /paasdata/offline/Deploy_Version_Package/CMS-PLAT/sources/Director/Director_pm_V8.25.10.01.n20241211102505.tar.gz

go clean --modcache
rm go.sum
go mod tidy

set setPgConfig end!!!

1、都没有对工程代码做code index。
2、cursor相对之前的工具，交流可更偏向自然语言。
3、复杂问题处理不好（需要继续拆分领域问题）

deepskr1
key：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
url：https://openrouter.ai/api/v1
模型：deepseek/deepseek-r1:free

sk-roKnmkIKckLIuW23rcf3CFnvwf3r8DJG1Gjr5FH3wgk1ZL21
https://api.moonshot.cn/v1
sk-6fRMr2JnpOWrIIiEG05OkgUiiZwzrqTOYw4FjKROkr6EZLLA
https://yibuapi.com/v1

sk-B7K20xh6pf1icfYRh5SVMg9LE49fgmrxjE5xex2Ni13YVCFY
export ANTHROPIC_API_KEY="sk-roKnmkIKckLIuW23rcf3CFnvwf3r8DJG1Gjr5FH3wgk1ZL21"
export ANTHROPIC_AUTH_TOKEN="sk-roKnmkIKckLIuW23rcf3CFnvwf3r8DJG1Gjr5FH3wgk1ZL21"
export ANTHROPIC_BASE_URL="https://api.moonshot.cn/v1"
con-9e1bc060d39a6a48e4dd683e30b9723a189c094dc9de2e16fec30404bdece337  NOVITA_API_KEY

GOARCH=ricsv64 GOOS=linux go build -ldflags "-s -w"
curl -H 'X-JFrog-Art-Api:AKCp8jQTbCbK7xD6a3W3Q97rZiAqS8n3rHNH2u5tvBjunrD1HLyJyr1S5YzUG3CodgqQzNFvT' -O \"https://artxa.zte.com.cn:443/artifactory/idn-alpha-generic/DexCloud/V3.25.10.06RC2/versions/DexCloud-RISCV3.25.10.06RC2/DexCloud_BaseImages/base-images/02_gobase/gobase-V3.25.10.06.n2502260904.tar.gz\"

curl -H 'X-JFrog-Art-Api:AKCp8jQTbCbK7xD6a3W3Q97rZiAqS8n3rHNH2u5tvBjunrD1HLyJyr1S5YzUG3CodgqQzNFvT' -O "https://artxa.zte.com.cn:443/artifactory/idn-alpha-generic/DexCloud/V3.25.10.06RC2/versions/DexCloud-RISCV3.25.10.06RC2/DexCloud_BaseImages/base-images/02_gobase/gobase-V3.25.10.06.n2502260904.tar.gz"
curl -H 'X-JFrog-Art-Api:AKCp8jQTbCbK7xD6a3W3Q97rZiAqS8n3rHNH2u5tvBjunrD1HLyJyr1S5YzUG3CodgqQzNFvT' -O "https://artxa.zte.com.cn:443/artifactory/idn-alpha-generic/DexCloud/V3.25.20.01/versions/DexCloud-RISCV3.25.20.01/DexCloud_BaseImages/base-images/02_gobase/gobase-V3.25.20.01.n2503120912.tar.gz"
curl -H 'X-JFrog-Art-Api:AKCp8jQTbCbK7xD6a3W3Q97rZiAqS8n3rHNH2u5tvBjunrD1HLyJyr1S5YzUG3CodgqQzNFvT' -O "https://artxa.zte.com.cn:443/artifactory/idn-alpha-generic/DexCloud/V3.25.20.01/versions/DexCloud-RISCV3.25.20.01/DexCloud_BaseImages/base-images/01_alpine-glibc-ext/alpine-glibc-ext-v3.20.1.f10.n2502100844.tar.gz"
TCF/devops/cms/config

今天cursor账号delete，再登录；报错Too many free trial accounts used on this machine.
修改~/.config/Cursor/User/globalStorage/storage.json ，telemetry.machineId telemetry.devDeviceId；
重启cursor，试用重回14天

天数160环境:10.166.220.108a
pict/PicT1!2@3#4$
root/RCms@Zte3
***************
/home/<USER>/ts_bi150_inference_server_V6.25.20B1004212200.tar.gz

dmidecode -t slot

bb3dd6bf3196
docker run --shm-size="32g" -itd --rm -v /usr/src:/usr/src -v /lib/modules:/lib/modules -v /dev:/dev -v /run/user/1001:/aih --name bi150_test --privileged --cap-add=ALL --network host bb3dd6bf3196  bash

python3 /opt/tritonserver/aih/contcroller/start_service.py 
--service-name "ts-vllm" 
--model-path "/aih/DeepSeek-R1-Distill-Qwen-32B" 
--pre-service-parameters '{}'  
--service-parameters '{"--tensor-parallel-size":4, "--port":4006,"--max_model_len":32768, "--gpu_memory_utilization":0.9, "--enable-chunked-prefill":"false","--trust_remote_code":true}'  
--rear-service-parameters '{}

export CUDA_VISIBLE_DEVICES=8,9,10,11,12,13,14,15;python3 /opt/tritonserver/aih/controller/start_service.py --service-name "ts-vllm" --model-path "/aih/DeepSeek-R1-Distill-Llama-70B" --pre-service-parameters '{}'  --service-parameters '{"--tensor-parallel-size":8, "--port":5326, "--max-model-len":10240, "--gpu-memory-utilization":0.9,"--enable-chunked-prefill":false,"--trust-remote-code":true, "--disable-log-requests":true}'  --rear-service-parameters '{}'

export CUDA_VISIBLE_DEVICES=8,9,10,11,12,13,14,15;
python3 /opt/tritonserver/aih/controller/start_service.py --service-name "ts-vllm" --model-path "/aih/DeepSeek-R1-Distill-Llama-70B"
--pre-service-parameters '{}'  --service-parameters '{"--tensor-parallel-size":8, "--port":5326, "--max-model-len":10240,
"--gpu-memory-utilization":0.9,"--enable-chunked-prefill":false,"--trust-remote-code":true, "--disable-log-requests":true}'  --rear-service-parameters '{}'

python3 benchmark_serving.py --host 0.0.0.0 --port 5326 --max-concurrency 64 --tokenizer /aih/DeepSeek-R1-Distill-Llama-70B --model /aih/DeepSeek-R1-Distill-Llama-70B --dataset-name random --num-prompts 64 --ignore-eos --random-input-len 128 --random-output-len 128 --save-result --metric-percentiles 10,50,90,95,99 --result-dir ./

export RESULT_DIR=/home/<USER>/op-aif-wsm
export RELEASE_DIR=/home/<USER>
export WORK_DIR=/home/<USER>
export workspace=/home/<USER>

${WORK_DIR}/op-aif-wsm/scripts/acp_scripts/buildriscv.sh ${workspace} v7.25.30.04.rv01.22763214 master
/jenkins/workspace/AC/RISCV_aif/v7.25.10.06_riscv/op-aif-wsm v7.25.10.06.rv01.22763214 master
${WORK_DIR}/op-aif-wsm/scripts/ci/releasearch.sh ${workspace} v7.25.10.06.rv01.22763214 master aarch64

${WORK_DIR}/volcano/acp-plcm/arm.sh ${WORK_DIR} v7.25.10.06.rv01.22763214 ${workspace}

${WORK_DIR}/volcano/acp-plcm/riscv.sh ${WORK_DIR} v7.25.20.06.rv01.22763214 ${workspace} master

docker run -it --rm --name=build_workspace --entrypoint=""  -v ${WORK_DIR}/op-aif-wsm:/go/src/work_space zxnp-pict-release-docker.artsh.zte.com.cn/stage/go-jdk-arm:stable /bin/sh

ssh -i /root/.ssh/id_rsa pict@***************
sftp -o ProxyJump=ubuntu@*************** zhuzhengnan@*************
sftp -o ProxyJump=ubuntu@*************** zhuzhengnan@*************

wget https://hf-mirror.com/hfd/hfd.sh
chmod a+x hfd.sh

export HF_ENDPOINT=http://hf-mirror.com
./hfd.sh deepseek-ai/DeepSeek-R1-Distill-Qwen-32B

***********************************

RDC:TCF-4977883 【cwsm】组件适配支持ZF2兼容（虚机部署， 物理机部署 ,支持RISC-V指令集 ）上电联调验证
RDC:TCF-5072017 【自提】wsm合并riscv分支流水线编译推送脚本

RDC:TCF-5071850 【自提】wsm合并riscv分支流水线编译推送脚本

sk-Ge4mFXbKcLx2Wce4943cE32b6bBe4f01A27648A4266f1c0d
https://api.juheai.top/v1

klx P800 单机推理命令

docker run -itd --rm
 -v /run/apts:/workspace
 -w /workspace
 --privileged
 --cap-add=SYS_PTRACE
 --name sz_distill_0811
 -v /etc/localtime:/etc/localtime:ro
 --net=host
 --shm-size 128G
 klx-llm-inference-server:V6.25.30B1208062200 bash

量化模型
python /opt/tritonserver/aih/services/klx/model/build_model.py --model-path /workspace/Qwen2.5-32B --serialize-path /workspace/Qwen2.5-32B-engines --tensor-parallel-size 2 --vendor klx
启动服务
python /opt/tritonserver/aih/controller/start_service.py --service-name "klx-distill" --model-path /workspace/Qwen2.5-32B-engines --service-parameters '{"--tensor-parallel-size": 2}'

python /opt/tritonserver/aih/controller/start_service.py --service-name "klx-distill" --model-path /workspace/Qwen2.5-32B-engines --tokenizer-path /workspace/Qwen2.5-32B --service-parameters '{"--tensor-parallel-size": 2}'

-v /dev:/dev
python3 benchmark_serving.py 
 --host 127.0.0.1 
 --port 8802 
 --backend vllm 
 --model /workspace/Qwen2.5-32B-engines 
 --dataset-name random 
 --num-prompts 100 \s
 --random-input-len 256 
 --random-output-len 256 
 --ignore-eos

python3 benchmark_serving.py 
 --host 0.0.0.0 
 --port 8802 
 --backend vllm 
 --model /workspace/Qwen2.5-32B-engines 
 --dataset-name random 
 --num-prompts 100 
 --random-input-len 256 
 --random-output-len 256 
 --ignore-eos

 benchmark：
 python3 benchmark_serving.py 
 --host 0.0.0.0 
 --port 8001 
 --backend vllm 
 --model /workspace/Qwen2.5-32B-engines/tokenizer 
 --dataset-name random 
 --num-prompts 100 
 --random-input-len 256 
 --random-output-len 256 
 --ignore-eos

  python3 benchmark_serving.py  --host 0.0.0.0  --port 8001
  --backend vllm  --model /workspace/Qwen2.5-32B-engines/tokenizer
  --dataset-name random
  --max-concurrency 64 --num-prompts 64
  --random-input-len 256  --random-output-len 256
  --ignore-eos --save-result --result-dir ./
